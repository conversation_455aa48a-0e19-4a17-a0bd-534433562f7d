package com.org.panaroma.web.strategy;

import com.org.panaroma.web.dto.detailAPI.RepeatPayment;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.web.dto.detailAPI.DetailInputParams;
import com.org.panaroma.commons.constants.WebConstants;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;

import lombok.extern.log4j.Log4j2;

import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.utils.CommonsUtility;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.ArrayList;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.TXN_DETAILS_REPEAT_PAYMENT_CTA_DISABLED_TXN_STATUS_LIST;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.TXN_DETAILS_REPEAT_PAYMENT_CTA_DISABLED_TXN_STATUS_FAILURE_ERROR_CODES_LIST;

/**
 * Service for handling Repeat Payment CTA logic for transaction details.
 */
@Service
@Log4j2
public class RepeatPaymentService {

	// Factory to get the correct repeat payment strategy for a transaction
	private final RepeatPaymentStrategyFactory repeatPaymentStrategyFactory;

	private final ConfigurablePropertiesHolder configurablePropertiesHolder;

	public RepeatPaymentService(RepeatPaymentStrategyFactory repeatPaymentStrategyFactory,
			ConfigurablePropertiesHolder configurablePropertiesHolder) {
		this.repeatPaymentStrategyFactory = repeatPaymentStrategyFactory;
		this.configurablePropertiesHolder = configurablePropertiesHolder;
	}

	public RepeatPayment getRepeatPayment(final TransformedTransactionHistoryDetail txn) {
		return computeRepeatPaymentDetails(null, null, txn, txn);
	}

	public RepeatPayment getRepeatPayment(final TransformedParticipant participantType,
			final TransformedTransactionHistoryDetail txn) {

		// Compute repeat payment details using the centralized provider
		return computeRepeatPaymentDetails(participantType, null, txn, txn);
	}

	public RepeatPayment getRepeatPayment(final TransformedTransactionHistoryDetail walletTxn,
			final TransformedTransactionHistoryDetail txn, final TransformedTransactionHistoryDetail upiTxn) {
		return computeRepeatPaymentDetails(null, walletTxn, txn, upiTxn);
	}

	/**
	 * Centralized method to compute and return the repeat payment details. Returns
	 * null if repeat payment is disabled for the transaction.
	 * This method is pure and does not mutate any input parameters.
	 */
	private RepeatPayment computeRepeatPaymentDetails(final TransformedParticipant participantType,
			final TransformedTransactionHistoryDetail walletTxn, final TransformedTransactionHistoryDetail txn,
			final TransformedTransactionHistoryDetail upiTxn) {
		// Check if repeat payment is disabled for this transaction
		if (isRepeatPaymentDisabled(txn)) {
			log.debug("Repeat payment is disabled for transaction: {} due to status/error code restrictions",
					txn != null ? txn.getTxnId() : "null");
			return null;
		}

		RepeatPaymentStrategy strategy = repeatPaymentStrategyFactory.getStrategy(txn);
		if (strategy != null) {
			// Check strategy-specific eligibility before proceeding
			if (!strategy.isTransactionEligibleForRepeatPayment(txn)) {
				log.debug("Repeat payment is not eligible for transaction: {} based on strategy-specific rules",
						txn != null ? txn.getTxnId() : "null");
				return null;
			}

			if (strategy instanceof P2mRepeatPaymentStrategy) {
				return strategy.getRepeatPaymentDetails(walletTxn, txn, upiTxn);
			}
			else {
				return strategy.getRepeatPaymentDetails(participantType, txn);
			}
		}
		return null;
	}

	/**
	 * Checks if repeat payment CTA should be disabled for the given transaction, based on
	 * status or error code.
	 */
	private boolean isRepeatPaymentDisabled(final TransformedTransactionHistoryDetail txn) {
		if (txn == null) {
			log.debug("Transaction is null, disabling repeat payment");
			return true;
		}
		try {
			List<String> repeatPaymentDisabledStatusList = configurablePropertiesHolder
				.getProperty(TXN_DETAILS_REPEAT_PAYMENT_CTA_DISABLED_TXN_STATUS_LIST, List.class);
			List<String> repeatPaymentDisabledFailureErrorCodesList = configurablePropertiesHolder
				.getProperty(TXN_DETAILS_REPEAT_PAYMENT_CTA_DISABLED_TXN_STATUS_FAILURE_ERROR_CODES_LIST, List.class);
			if (repeatPaymentDisabledStatusList == null) {
				repeatPaymentDisabledStatusList = new ArrayList<>();
			}
			if (repeatPaymentDisabledFailureErrorCodesList == null) {
				repeatPaymentDisabledFailureErrorCodesList = new ArrayList<>();
			}
			if (txn.getStatus() != null) {
				try {
					ClientStatusEnum statusEnum = ClientStatusEnum.getStatusEnumByKey(txn.getStatus());
					if (statusEnum != null && repeatPaymentDisabledStatusList.contains(statusEnum.toString())) {
						log.debug("Repeat payment disabled for transaction: {} due to status: {}", txn.getTxnId(),
								statusEnum);
						return true;
					}
				}
				catch (Exception e) {
					log.warn("Failed to parse status for transaction: {}, status: {}, error: {}", txn.getTxnId(),
							txn.getStatus(), e.getMessage());
				}
			}
			if (txn.getContextMap() != null) {
				String errorCode = txn.getContextMap().get(WebConstants.ERROR_CODE);
				if (errorCode != null && repeatPaymentDisabledFailureErrorCodesList.contains(errorCode)) {
					log.debug("Repeat payment disabled for transaction: {} due to error code: {}", txn.getTxnId(),
							errorCode);
					return true;
				}
			}
			return false;
		}
		catch (Exception e) {
			log.error("Error checking repeat payment disabled status for transaction: {}, error: {}", txn.getTxnId(),
					CommonsUtility.exceptionFormatter(e));
			return true;
		}
	}

}